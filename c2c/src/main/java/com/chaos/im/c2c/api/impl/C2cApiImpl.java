package com.chaos.im.c2c.api.impl;

import cn.hutool.core.bean.BeanUtil;
import cn.hutool.json.JSONUtil;
import com.chaos.im.c2c.api.C2cApi;
import com.chaos.im.c2c.api.domain.LastC2cMessageDTO;
import com.chaos.im.c2c.domain.C2cMessageAckDO;
import com.chaos.im.c2c.domain.C2cMessageBO;
import com.chaos.im.c2c.mapper.C2cMessageAckMapper;
import com.chaos.im.c2c.service.MessageSendService;
import com.chaos.keep.alive.common.im.constant.ImConstants;
import lombok.AllArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.apache.dubbo.config.annotation.DubboService;
import org.jetbrains.annotations.Nullable;
import org.redisson.api.RMap;
import org.redisson.api.RedissonClient;
import org.springframework.beans.BeanUtils;
import org.springframework.data.domain.Sort;
import org.springframework.data.mongodb.core.MongoTemplate;
import org.springframework.data.mongodb.core.query.Criteria;
import org.springframework.data.mongodb.core.query.Query;
import org.springframework.util.StopWatch;

import javax.annotation.Resource;

@AllArgsConstructor
@DubboService(version = "1.0.0", interfaceClass = C2cApi.class)
@Slf4j
public class C2cApiImpl implements C2cApi {

    @Resource
    private C2cMessageAckMapper c2cMessageAckMapper;

    private final MongoTemplate mongoTemplate;

    @Resource
    private MessageSendService messageSendService;

    @Resource
    private RedissonClient redissonClient;


    C2cMessageBO getLastMessageByChatId(Long chatId) {


        Criteria criteria = Criteria.where("chatId").is(chatId);

        // 构建查询对象
        Query query = new Query(criteria);

        query.with(Sort.by(Sort.Direction.DESC, "_id")).limit(1); // 按消息ID降序排序

        // 执行查询,查出这个会话的最新消息
        C2cMessageBO c2cMessageBO = mongoTemplate.findOne(query, C2cMessageBO.class);

        return c2cMessageBO;
    }

    C2cMessageBO getLastMessageByChatIdNotMe(Long chatId, String operatorNo) {
        Criteria criteria2 = Criteria.where("chatId").is(chatId).and("fromOperatorNo").ne(operatorNo);
        Query query2 = new Query(criteria2);

        query2.with(Sort.by(Sort.Direction.DESC, "_id")).limit(1); // 按消息ID降序排序


        // 执行查询
        C2cMessageBO lastC2cMessageNotMe = mongoTemplate.findOne(query2, C2cMessageBO.class);

        return lastC2cMessageNotMe;
    }

    @Override
    public LastC2cMessageDTO getLastMessageByChatIdAndOperatorNo(Long chatId, String operatorNo, String deviceId) {

        LastC2cMessageDTO lastC2cMessageDTO = new LastC2cMessageDTO();

        StopWatch lastMessageStopWatch = new StopWatch();
        lastMessageStopWatch.start("getLastC2cMessageNotMe chatId:"+chatId+" operatorNo:"+operatorNo);
        C2cMessageBO lastC2cMessageNotMe = getLastC2cMessageNotMe(chatId, operatorNo, lastC2cMessageDTO);

        lastMessageStopWatch.stop();
        log.info(lastMessageStopWatch.prettyPrint());

        StopWatch unReadCountStopWatch = new StopWatch();
        unReadCountStopWatch.start("setUnReadCount chatId:"+chatId+" operatorNo:"+operatorNo);

        setUnReadCount(chatId, operatorNo, deviceId, lastC2cMessageNotMe, lastC2cMessageDTO);
        unReadCountStopWatch.stop();
        log.info(unReadCountStopWatch.prettyPrint());

        return lastC2cMessageDTO;
    }

    private void setUnReadCount(Long chatId, String operatorNo, String deviceId, C2cMessageBO lastC2cMessageNotMe, LastC2cMessageDTO lastC2cMessageDTO) {
        C2cMessageAckDO c2cMessageAckDO = messageSendService.lastAck(chatId, operatorNo, deviceId);

        if (c2cMessageAckDO != null && lastC2cMessageNotMe != null && c2cMessageAckDO.getLastAckMessageId().equals(lastC2cMessageNotMe.getId())) {
            lastC2cMessageDTO.setUnReadCount(0);
        } else {
            //未读数不为0
            long allMessageCountStartTime = System.currentTimeMillis();
            if (lastC2cMessageNotMe != null && c2cMessageAckDO != null) {
                //提交过ack
                if (lastC2cMessageNotMe.getSequence() != null && c2cMessageAckDO.getSequence() != null) {
                    lastC2cMessageDTO.setUnReadCount(lastC2cMessageNotMe.getSequence() - c2cMessageAckDO.getSequence());
                }
            } else {
                //从未提交过ack
                String unReadCountKey = ImConstants.C2C_UNREAD_COUNT_PREFIX + chatId + ImConstants.SEPARATOR + operatorNo;
                long count = redissonClient.getAtomicLong(unReadCountKey).get();
                lastC2cMessageDTO.setUnReadCount(count);

            }
            long allMessageCountEndTime = System.currentTimeMillis();
            log.info("allMessageCount cost:{} , chatId:{} , operatorNo:{}",allMessageCountEndTime-allMessageCountStartTime,chatId,operatorNo);

        }
        if (lastC2cMessageDTO.getUnReadCount() < 0) {
            lastC2cMessageDTO.setUnReadCount(0);
        }
    }

    @Nullable
    private C2cMessageBO getLastC2cMessageNotMe(Long chatId, String operatorNo, LastC2cMessageDTO lastC2cMessageDTO) {
        String lastMessageKey = ImConstants.C2C_LAST_MESSAGE_PREFIX+ chatId + ImConstants.SEPARATOR + operatorNo;

        RMap<Object, Object> beanMap = redissonClient.getMap(lastMessageKey);
        C2cMessageBO lastC2cMessageNotMe = null;
        if (beanMap != null && !beanMap.isEmpty()) {
            log.info("get lastMsg from redis :{}", JSONUtil.toJsonStr(beanMap));
            lastC2cMessageNotMe = BeanUtil.mapToBean(beanMap, C2cMessageBO.class, true);
            BeanUtils.copyProperties(lastC2cMessageNotMe, lastC2cMessageDTO);
            lastC2cMessageDTO.setMessageId(lastC2cMessageNotMe.getId());
        } else {
//            lastC2cMessageNotMe = getLastMessageByChatIdNotMe(chatId, operatorNo);
//            if (lastC2cMessageNotMe != null) {
//                BeanUtils.copyProperties(lastC2cMessageNotMe, lastC2cMessageDTO);
//                log.info("get lastMsg from mongdb :{}", JSONUtil.toJsonStr(lastC2cMessageNotMe));
//                lastC2cMessageDTO.setMessageId(lastC2cMessageNotMe.getId());
//            }
        }
        return lastC2cMessageNotMe;
    }

    @Override
    public LastC2cMessageDTO getLastMessageAckFromDB(Long chatId, String operatorNo, String deviceId) {

        C2cMessageAckDO c2cMessageAckDO = messageSendService.lastAck(chatId, operatorNo, deviceId);
        LastC2cMessageDTO lastC2cMessageDTO = new LastC2cMessageDTO();
        BeanUtils.copyProperties(c2cMessageAckDO, lastC2cMessageDTO);
        return lastC2cMessageDTO;
    }

}
