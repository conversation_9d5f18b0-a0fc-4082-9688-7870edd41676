package com.chaos.im.c2c.controller;

import com.chaos.im.c2c.api.domain.FetchQuery;
import com.chaos.im.c2c.service.MessageSendService;
import com.outstanding.framework.core.ResponseDTO;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

/**
 * <AUTHOR>
 */
@RestController
@RequestMapping("/fetch/")
public class MessageFetchController {

    @Autowired
    private MessageSendService messageSendService;

    @PostMapping("/offline/msg")
    public ResponseDTO<?> fetch(@RequestBody FetchQuery fetchQuery) {
        return ResponseDTO.creatDTO(messageSendService.fetch(
                fetchQuery.getChatId(),
                fetchQuery.getOperatorNo(),
                fetchQuery.getDeviceId(),
                fetchQuery.getStartMessageId(),
                fetchQuery.getStopMessageId(),
                fetchQuery.getPageSize(),fetchQuery.getCurrent(),fetchQuery.getSort()));
    }

    @PostMapping("/lastMessageAck")
    public ResponseDTO<?> getLastMessageAck(@RequestBody FetchQuery fetchQuery) {
        return ResponseDTO.creatDTO(messageSendService.lastAckMessageId(
                fetchQuery.getChatId(),
                fetchQuery.getOperatorNo(),
                fetchQuery.getDeviceId()));
    }
}
