package com.chaos.im.c2c.controller;

import cn.hutool.core.util.ObjectUtil;
import com.chaos.im.c2c.domain.C2cMessageBO;
import com.chaos.im.c2c.domain.TestSendMsgReq;
import com.chaos.im.chat.api.ChatApi;
import com.chaos.im.chat.domain.ChatMemberApiVO;
import com.chaos.keep.alive.common.im.constant.Constants;
import com.chaos.keep.alive.im.id.server.api.IdServerApi;
import com.chaos.usercenter.api.UserOperatorFacade;
import com.chaos.usercenter.api.dto.resp.OperatorInfoRespDTO;
import com.outstanding.framework.core.ResponseDTO;
import org.apache.dubbo.config.annotation.DubboReference;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.mongodb.core.MongoTemplate;
import org.springframework.data.redis.core.RedisTemplate;
import org.springframework.web.bind.annotation.*;

import javax.validation.Valid;
import java.util.List;
import java.util.Optional;

@RestController
@RequestMapping("/test/")
public class TestController {

    @Autowired
    private UserOperatorFacade userOperatorFacade;

    @Autowired
    private RedisTemplate<String, Long> redisTemplate;

    @Autowired
    private MongoTemplate mongoTemplate;

    @DubboReference(version = "1.0.0")
    private IdServerApi idServerApi;

    @DubboReference(version = "1.0.0")
    private ChatApi chatApi;

    @GetMapping("/sendMsgByOperatorNo")
    public ResponseDTO<?> sendMsgByOperatorNo(String  op){

        List<ChatMemberApiVO> chatMemberApiVO = chatApi.getChatMemberByOperatorNo(op);

        chatMemberApiVO.forEach(cm -> {
            if(cm.getType()==1) {
                TestSendMsgReq testSendMsgReq = new TestSendMsgReq();
                testSendMsgReq.setToOperatorNo(cm.getPeerId());
                testSendMsgReq.setContent("test");
                testSendMsgReq.setFromOperatorNo(cm.getMemberId());
                testSendMsgReq.setCategory(1);
                testSendMsgReq.setInsertNum(1);
                testSendMsgReq.setChatId(cm.getChatId());
                sendMsg(testSendMsgReq);
            }
        });

        return ResponseDTO.creatDTO();

    }

    @PostMapping("/sendMsg")
    public ResponseDTO<?> sendMsg(@RequestBody @Valid TestSendMsgReq request) {

        int insertNum = request.getInsertNum()==null?10:request.getInsertNum();
        for (int i = 0; i < insertNum; i++) {

            if (ObjectUtil.isNull(request.getFromOperatorNo())) {
                String loginName = request.getFromMobile();
                OperatorInfoRespDTO operatorInfoRespDTO = userOperatorFacade.getOperatorInfoByLoginName(loginName, "SuperApp");
                request.setFromOperatorNo(operatorInfoRespDTO.getOperatorNo());
            }

            if (ObjectUtil.isNull(request.getToOperatorNo())) {
                OperatorInfoRespDTO operatorInfoRespDTO2 = userOperatorFacade.getOperatorInfoByLoginName(request.getToMobile(), "GoNowDriver");
                String operatorNo2 = operatorInfoRespDTO2.getOperatorNo();
                request.setToOperatorNo(operatorNo2);
            }


            C2cMessageBO c2cMessageBO = new C2cMessageBO();
            c2cMessageBO.setId(idServerApi.genId("message"));
            c2cMessageBO.setCategory(request.getCategory());
            c2cMessageBO.setAppId("GoNowDriver");
            c2cMessageBO.setFromOperatorNo(request.getFromOperatorNo());
            c2cMessageBO.setToOperatorNo(request.getToOperatorNo());
            c2cMessageBO.setContent(request.getContent() + ":" + i);
            c2cMessageBO.setChatId(request.getChatId());
            c2cMessageBO.setMessageId(request.getChatId() + "-" + c2cMessageBO.getId());
            c2cMessageBO.setTimestamp(System.currentTimeMillis());

            Optional<Long> sequenceOptional = Optional.ofNullable(redisTemplate.boundValueOps(Constants.REDIS_SEQ_KEY + "::" + request.getChatId()).increment());
            // 服务端设置sequence

            c2cMessageBO.setSequence(sequenceOptional.orElse(1L));

            mongoTemplate.save(c2cMessageBO);
        }

        return ResponseDTO.creatDTO();
    }

}
