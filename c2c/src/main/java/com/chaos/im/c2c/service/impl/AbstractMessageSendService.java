package com.chaos.im.c2c.service.impl;

import com.alibaba.fastjson.JSONObject;
import com.chaos.im.c2c.dao.C2cMessageAckDAO;
import com.chaos.im.c2c.domain.C2cMessageAckDO;
import com.chaos.im.c2c.service.MessageSendService;
import com.chaos.keep.alive.common.im.constant.Constants;
import com.chaos.keep.alive.common.im.constant.ImConstants;
import com.chaos.keep.alive.common.im.domain.MessageJsonFetch;
import com.chaos.keep.alive.common.im.domain.MessageJsonPush;
import com.chaos.keep.alive.common.im.domain.MessageSendJsonRequest;
import com.chaos.keep.alive.common.im.domain.MessageSendJsonResponse;
import org.redisson.api.RedissonClient;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.kafka.core.KafkaTemplate;
import org.springframework.transaction.annotation.Transactional;


/**
 * <AUTHOR>
 */
public abstract class AbstractMessageSendService implements MessageSendService {

    private static final String ACK_KEY_PREFIX = "IM::C2C_MESSAGE_ACK";

    private static final String ACK_COUNT_KEY_PREFIX = "IM::C2C_MESSAGE_ACK_COUNT";

    private static final Integer ACK_THRESHOLD = 1;

    @Value("#{'topic-message-send-response-'.concat('${spring.profiles.active}')}")
    private String TOPIC_MESSAGE_SEND_RESPONSE;

    @Value("#{'topic-message-push-'.concat('${spring.profiles.active}')}")
    private String TOPIC_MESSAGE_PUSH;

    @Value("#{'topic-message-fetch-'.concat('${spring.profiles.active}')}")
    private String TOPIC_MESSAGE_FETCH;

    @Autowired
    private RedissonClient redissonClient;

    @Autowired
    protected KafkaTemplate<String, String> kafkaTemplate;

    @Autowired
    protected C2cMessageAckDAO c2cMessageAckDAO;

    @Override
    @Transactional
    public void updateAck(Long chatId, String memberId, String clientId, Long messageId, Long sequence) {

        long count = c2cMessageAckDAO.count(chatId, memberId, clientId);
        if (count == 0) {
            C2cMessageAckDO c2cMessageAckDO = new C2cMessageAckDO();
            c2cMessageAckDO.setChatId(chatId);
            c2cMessageAckDO.setMemberId(memberId);
            c2cMessageAckDO.setDeviceId(clientId);
            c2cMessageAckDO.setLastAckMessageId(messageId);
            c2cMessageAckDO.setSequence(sequence);
            c2cMessageAckDAO.save(c2cMessageAckDO);
        } else {
            Long lastAck = c2cMessageAckDAO.getLastAckMessageId(chatId, memberId, clientId);
            if (lastAck != null && lastAck < messageId) {
                String unReadCountKey = ImConstants.C2C_UNREAD_COUNT_PREFIX+ chatId+ImConstants.SEPARATOR+memberId;
                redissonClient.getAtomicLong(unReadCountKey).set(0);
                c2cMessageAckDAO.updateAck(chatId, memberId, clientId, messageId, sequence);
            }
        }

    }

    protected void sendMessageSendResponse(MessageSendJsonRequest request) {
        MessageSendJsonResponse message = new MessageSendJsonResponse();
        message.setMessageId(request.getMessageId());
        message.setChatId(request.getChatId());
        message.setChatType(request.getChatType());
        message.setFromOperatorNo(request.getFromOperatorNo());
        message.setToOperatorNo(request.getToOperatorNo());
        message.setSequence(request.getSequence());
        kafkaTemplate.send(TOPIC_MESSAGE_SEND_RESPONSE, JSONObject.toJSONString(message));
    }

    protected void sendMessagePush(MessageSendJsonRequest request) {
        MessageJsonPush message = new MessageJsonPush();
        message.setMessageId(request.getMessageId());
        message.setChatType(request.getChatType());
        message.setFromOperatorNo(request.getFromOperatorNo());
        message.setToOperatorNo(request.getToOperatorNo());
        message.setChatId(request.getChatId());
        message.setContent(request.getContent());
        message.setSequence(request.getSequence());
        message.setCategory(request.getCategory());
        message.setToAppId(request.getToAppId());
        kafkaTemplate.send(TOPIC_MESSAGE_PUSH, JSONObject.toJSONString(message));
    }

    protected void sendMessageFetch(Long chatId, String toId) {
        MessageJsonFetch message = new MessageJsonFetch();
        message.setToOperatorNo(toId);
        message.setChatId(chatId);
        message.setChatType(Constants.CHAT_TYPE_C2C);
        kafkaTemplate.send(TOPIC_MESSAGE_FETCH, JSONObject.toJSONString(message));
    }

    public abstract C2cMessageAckDO lastAck(Long chatId, String operatorNo, String deviceId);
}
