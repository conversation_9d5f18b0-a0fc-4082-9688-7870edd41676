package com.chaos.im.c2c.service.impl;

import com.chaos.im.c2c.domain.FriendRequestDTO;
import com.chaos.im.c2c.domain.FriendRequestQuery;
import com.chaos.im.c2c.service.FriendRequestService;
import com.chaos.keep.alive.common.core.domain.BasePage;
import org.springframework.stereotype.Service;


/**
 * <AUTHOR>
 */
@Service
public class FriendRequestServiceImpl implements FriendRequestService {


    @Override
    public void submit(FriendRequestDTO friendRequestDTO) {

    }

    @Override
    public BasePage<FriendRequestDTO> list(FriendRequestQuery query) {
        return null;
    }
}
