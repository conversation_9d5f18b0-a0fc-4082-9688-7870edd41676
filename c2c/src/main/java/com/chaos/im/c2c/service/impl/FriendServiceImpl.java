package com.chaos.im.c2c.service.impl;

import com.chaos.im.c2c.dao.FriendRelationshipDAO;
import com.chaos.im.c2c.dao.FriendRequestDAO;
import com.chaos.im.c2c.domain.*;
import com.chaos.im.c2c.service.FriendRelationshipService;
import com.chaos.im.c2c.service.FriendService;
import com.chaos.im.chat.api.ChatApi;
import com.chaos.keep.alive.common.core.domain.BasePage;
import com.chaos.keep.alive.common.core.util.BeanCopierUtils;
import com.chaos.usercenter.api.UserOperatorFacade;
import com.chaos.usercenter.api.dto.resp.UserOperatorInfoForImDTO;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.apache.dubbo.config.annotation.DubboReference;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;


/**
 * <AUTHOR>
 */
@Service
@RequiredArgsConstructor
@Slf4j
public class FriendServiceImpl implements FriendService {


    private final FriendRelationshipDAO friendRelationshipDAO;

    private final FriendRelationshipService friendRelationshipService;

    @DubboReference(version = "1.0.0")
    private ChatApi chatApi;

    @DubboReference(version = "1.0.0")
    private UserOperatorFacade userOperatorFacade;

    @Override
    @Transactional
    public AddFriendResponse addFriend(String operatorNo, String friendOperatorNo) {

        FindFriendResponse friend =  findByOperatorNo(friendOperatorNo);

        FriendRelationshipDO relationshipDO1 = new FriendRelationshipDO();
        relationshipDO1.setOperatorNo(operatorNo);
        relationshipDO1.setFriendOperatorNo(friendOperatorNo);
        relationshipDO1.setNickname(friend.getNickname());
        addFriend(relationshipDO1);

        FindFriendResponse friend2 =  findByOperatorNo(operatorNo);
        FriendRelationshipDO relationshipDO2 = new FriendRelationshipDO();
        relationshipDO2.setOperatorNo(friendOperatorNo);
        relationshipDO2.setFriendOperatorNo(operatorNo);
        relationshipDO2.setNickname(friend2.getNickname());
        addFriend(relationshipDO2);

        AddFriendResponse addFriendResponse = new AddFriendResponse();
        addFriendResponse.setOperatorNo(friend.getOperatorNo());
        addFriendResponse.setNickname(friend.getNickname());
        addFriendResponse.setGender(friend.getGender());
        addFriendResponse.setAvatar(friend.getAvatar());

       return addFriendResponse;

    }

    @Override
    public BasePage<FriendRelationshipDTO> listByPage(FriendRelationshipQuery query) {
        return BeanCopierUtils.convert(friendRelationshipDAO.listByPage(query), FriendRelationshipDTO.class);
    }

    @Override
    public FindFriendResponse findByOperatorNo(String operatorNo) {
        UserOperatorInfoForImDTO operatorInfoRespDTO = userOperatorFacade.getOperatorInfoForIM(operatorNo);

        FindFriendResponse findFriendResponse = new FindFriendResponse();
        findFriendResponse.setOperatorNo(operatorInfoRespDTO.getOperatorNo());
        findFriendResponse.setNickname(operatorInfoRespDTO.getNickName());
        findFriendResponse.setAvatar(operatorInfoRespDTO.getHeadURL());
        findFriendResponse.setAppId(operatorInfoRespDTO.getAppId());
        findFriendResponse.setMobile(operatorInfoRespDTO.getMobile());
        findFriendResponse.setUserInfo(operatorInfoRespDTO.getUserInfo());
        return findFriendResponse;
    }

    @Override
    public void deleteByOperatorNo(String operatorNo, String friendOperatorNo) {
        friendRelationshipService.removeByOperatorNo(operatorNo,friendOperatorNo);
    }

    private void addFriend(FriendRelationshipDO friendRelationshipDO) {

       Integer count =  friendRelationshipService.count(friendRelationshipDO.getOperatorNo(), friendRelationshipDO.getFriendOperatorNo());

       if(count>0){
           log.info("好友关系已经存在 operatorNo:{},friendOperatorNo:{}",friendRelationshipDO.getOperatorNo(),friendRelationshipDO.getFriendOperatorNo());
           return ;
       }

        friendRelationshipDAO.save(friendRelationshipDO);
//        ChatDTO chatDTO = new ChatDTO();
//        chatDTO.setType(Constants.CHAT_TYPE_C2C);
//        chatDTO.setMemberId(friendRelationshipDO.getOperatorNo());
//        chatDTO.setPeerId(friendRelationshipDO.getFriendOperatorNo());
//        chatDTO.setNickname(friendRelationshipDO.getNickname());
//        chatApi.addChat(chatDTO);
    }
}
