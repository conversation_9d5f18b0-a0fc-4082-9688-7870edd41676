package com.chaos.im.c2g.group.api.domain;

import com.chaos.keep.alive.common.core.domain.BaseDomain;
import lombok.Data;


@Data
public class LastMessageVO extends BaseDomain {

    private static final long serialVersionUID = 1L;

    private Long chatId;
    private Long groupId;
    private Long messageId;
    private String fromOperatorNo;
    private String toOperatorNo;
    private Integer category;
    private String content;
    private String appId;
    private Long sequence;
    private Long timestamp;
    private Long unReadCount;

}