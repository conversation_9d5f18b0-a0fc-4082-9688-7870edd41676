package com.chaos.im.c2g.group.api.impl;

import com.chaos.im.c2g.group.api.GroupApi;
import com.chaos.im.c2g.group.api.domain.*;
import com.chaos.im.c2g.group.domain.*;
import com.chaos.im.c2g.group.service.GroupMemberService;
import com.chaos.im.c2g.group.service.GroupService;
import com.chaos.im.c2g.group.service.MessageSendService;
import com.chaos.im.chat.api.ChatApi;
import com.chaos.keep.alive.common.core.util.BeanCopierUtils;
import com.chaos.keep.alive.common.core.util.ResultHelper;
import com.chaos.keep.alive.common.im.constant.Constants;
import com.outstanding.framework.core.ResponseDTO;
import lombok.AllArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.apache.dubbo.config.annotation.DubboReference;
import org.apache.dubbo.config.annotation.DubboService;
import org.springframework.transaction.annotation.Transactional;

import javax.annotation.Resource;
import java.util.List;
import java.util.concurrent.*;

/**
 * <AUTHOR>
 */
@AllArgsConstructor
@DubboService(version = "1.0.0", interfaceClass = GroupApi.class)
@Slf4j
public class GroupApiImpl implements GroupApi {

    @Resource
    private GroupMemberService groupMemberService;

    @Resource
    private GroupService groupService;


    @Resource
    private MessageSendService messageSendService;

    @DubboReference(version = "1.0.0")
    private ChatApi chatApi;

    private static final int coreSize = 20;
    private static final int maxSize = coreSize * 2;
    private static final BlockingQueue<Runnable> queue = new ArrayBlockingQueue<>(1000); // 有界队列


    // 创建线程池
    private static final ThreadPoolExecutor executor = new ThreadPoolExecutor(
            coreSize,
            maxSize,
            60,
            TimeUnit.SECONDS,
            queue,
            Executors.defaultThreadFactory(),
            new ThreadPoolExecutor.AbortPolicy()
    );


    // private static final String EX_CUSTOMER_SERVICE_STATUS = "customerServiceStatus";

    @Override
    public List<String> getMemberIdByGroupId(Long groupId) {
        return groupMemberService.getByGroupId(groupId);
    }

    @Override
    public ResponseDTO<?> create(String ownerId, String name) {

        GroupVO groupVO = new GroupVO();
        groupVO.setOwnerId(ownerId);
        groupVO.setName(name);

        Long groupId = groupService.create(groupVO.clone(GroupDTO.class));
        return ResponseDTO.creatDTO(groupId);
    }

    @Override
    public ResponseDTO<?> create(String ownerId, String name, String ex,String faceUrl) {
        GroupVO groupVO = new GroupVO();
        groupVO.setOwnerId(ownerId);
        groupVO.setName(name);
        groupVO.setEx(ex);
        groupVO.setFaceUrl(faceUrl);

        Long groupId = groupService.create(groupVO.clone(GroupDTO.class));
        return ResponseDTO.creatDTO(groupId);
    }

    @Override
    public ResponseDTO<?> dismiss(Long groupId) {
        executor.execute(() -> {
            try {
                groupService.dismiss(groupId);
                chatApi.deleteChatByGroupId(Constants.CHAT_TYPE_C2G, groupId);
            } catch (Exception e) {
                log.error("解散群聊失败", e);
            }
        });
        return ResultHelper.ok();
    }

    @Override
    @Transactional
    public ResponseDTO<Long> createAndInvite(CreateGroupAndInviteReq req) {
        return groupService.createAndInvite(req.getGroup(), req.getMemberOperatorNos());
    }

    @Override
    public ResponseDTO<?> findGroupByGroupId(Long groupId) {
        return ResultHelper.ok(groupService.findByGroupId(groupId));
    }

    @Override
    public ResponseDTO<?> kick(Long groupId, List<String> memberOperatorNos) {
        executor.execute(() -> {
            log.info("dubbo调用踢出群聊 groupId:{}, memberOperatorNos:{}", groupId, memberOperatorNos);
            try {
                for (String memberId : memberOperatorNos) {
                    groupMemberService.kick(groupId, memberId);
                    chatApi.deleteChatByGroupId(Constants.CHAT_TYPE_C2G, groupId, memberId);
                }
            } catch (Exception e) {
                log.error("踢出群聊失败", e);
            }
        });


        return ResultHelper.ok();

    }

    @Override
    @Transactional
    public ResponseDTO<?> invite(Long groupId, List<String> memberOperatorNos) {

        for (String memberId : memberOperatorNos) {
            groupMemberService.join(groupId, memberId);
        }
        return ResultHelper.ok();

    }

    @Override
    public ResponseDTO<?> findMemberByGroupId(Long groupId, Integer pageSize, Integer current) {

        GroupMemberQuery query = new GroupMemberQuery();
        query.setGroupId(groupId);
        query.setCurrent(current);
        query.setPageSize(pageSize);

        return ResultHelper.ok(BeanCopierUtils.convert(groupMemberService.listGroupMember(query), GroupMemberVO.class));

    }

    @Override
    public LastC2gMessageDTO getLastMessageByChatIdAndOperatorNo(Long chatId, String operatorNo, String deviceId) {
        return messageSendService.getLastMessageByChatIdAndOperatorNo(chatId, operatorNo, deviceId);
    }

    @Override
    public GroupVO queryByOrderNo(String ownerId, String orderNo, Integer bizType) {
        GroupDO groupDO = groupService.queryByOrderNo(ownerId, orderNo, bizType);
        GroupVO groupVO = new GroupVO();
        if(groupDO==null){
            return null;
        }
        BeanCopierUtils.copy(groupDO, groupVO);

        return groupVO;
    }
}
