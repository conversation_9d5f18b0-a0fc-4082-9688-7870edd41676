package com.chaos.im.c2g.group.controller;


import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import com.chaos.im.c2g.group.api.domain.MessageSendReq;
import com.chaos.im.c2g.group.service.MessageSendService;
import com.outstanding.framework.core.ResponseDTO;

import lombok.extern.slf4j.Slf4j;

@RestController
@RequestMapping("/message/sender")
@Slf4j
public class MessageSendController {

    @Autowired
    private MessageSendService messageSendService;

    @PostMapping("/group/message")
    public ResponseDTO<?> send(@RequestBody MessageSendReq body) {
       
        messageSendService.sendMessage(body);
        return ResponseDTO.creatDTO();
    }
}
