package com.chaos.im.c2g.group.domain;

import com.baomidou.mybatisplus.annotation.TableName;
import com.chaos.keep.alive.common.persistent.domain.BaseDO;
import lombok.Getter;
import lombok.Setter;


/**
 * <AUTHOR>
 */
@Getter
@Setter
@TableName("group_info")
public class GroupDO extends BaseDO {

    /**
     * 群主id
     */
    private String ownerId;

    /**
     * 群名
     */
    private String name;

    private String faceUrl;

    private String notice;

    private String ex;

    private Integer status;
}