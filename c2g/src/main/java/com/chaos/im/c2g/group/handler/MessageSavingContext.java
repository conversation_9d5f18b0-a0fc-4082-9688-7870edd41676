package com.chaos.im.c2g.group.handler;

import org.apache.dubbo.common.extension.ExtensionLoader;
import org.springframework.stereotype.Component;

@Component
public class MessageSavingContext {

    public  IMessageWriteHandler init() {

        LastMessageWriteHandler lastMessageWriteHandler = (LastMessageWriteHandler) ExtensionLoader.getExtensionLoader(IMessageWriteHandler.class).getExtension("lastMsg");
        UnReadCountWriteHandler unReadCountWriteHandler = (UnReadCountWriteHandler)ExtensionLoader.getExtensionLoader(IMessageWriteHandler.class).getExtension("unReadCount");
        C2gMessageWriteHandler c2gMessageWriteHandler = (C2gMessageWriteHandler) ExtensionLoader.getExtensionLoader(IMessageWriteHandler.class).getExtension("c2gMessage");
        lastMessageWriteHandler.setNextHandler(unReadCountWriteHandler);
        unReadCountWriteHandler.setNextHandler(c2gMessageWriteHandler);

        return lastMessageWriteHandler;
    }
}
