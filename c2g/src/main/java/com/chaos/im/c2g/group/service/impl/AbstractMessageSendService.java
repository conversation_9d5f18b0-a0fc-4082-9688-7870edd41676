package com.chaos.im.c2g.group.service.impl;

import com.alibaba.fastjson.JSONObject;
import com.chaos.im.c2g.group.service.MessageSendService;
import com.chaos.keep.alive.common.im.constant.Constants;
import com.chaos.keep.alive.common.im.domain.MessageJsonFetch;
import com.chaos.keep.alive.common.im.domain.MessageJsonPush;
import com.chaos.keep.alive.common.im.domain.MessageSendJsonRequest;
import com.chaos.keep.alive.common.im.domain.MessageSendJsonResponse;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.data.redis.core.RedisTemplate;
import org.springframework.kafka.core.KafkaTemplate;

import java.util.List;
import java.util.Objects;

/**
 * <AUTHOR>
 */
@Slf4j
public abstract class AbstractMessageSendService implements MessageSendService {

    @Value("#{'topic-message-send-response-'.concat('${spring.profiles.active}')}")
    private String TOPIC_MESSAGE_SEND_RESPONSE;

    @Value("#{'topic-message-push-'.concat('${spring.profiles.active}')}")
    private String TOPIC_MESSAGE_PUSH;

    @Value("#{'topic-message-fetch-'.concat('${spring.profiles.active}')}")
    private String TOPIC_MESSAGE_FETCH;

    @Autowired
    protected RedisTemplate<String, Object> redisTemplate;

    @Autowired
    protected KafkaTemplate<String, String> kafkaTemplate;


    public void sendMessageSendResponse(MessageSendJsonRequest request) {
        MessageSendJsonResponse message = new MessageSendJsonResponse();
        message.setChatId(request.getChatId());
        message.setMessageId(request.getMessageId());
        message.setFromOperatorNo(request.getFromOperatorNo());
        message.setToOperatorNo(StringUtils.isEmpty(request.getToOperatorNo()) ? request.getFromOperatorNo() : request.getToOperatorNo());
        message.setChatType(request.getChatType());
        message.setSequence(request.getSequence());
        kafkaTemplate.send(TOPIC_MESSAGE_SEND_RESPONSE, JSONObject.toJSONString(message));
    }

    public void sendMessagePush(MessageSendJsonRequest request, List<String> memberIds, Long groupId) {
        for (String memberId : memberIds) {
            if (!Objects.equals(memberId, request.getFromOperatorNo())) {
                sendMessagePush(request, memberId, groupId);
            }
        }
    }

    public void sendMessagePush(MessageSendJsonRequest request, String memberId, Long groupId) {
        log.debug(request.toJsonStr());
        MessageJsonPush message = new MessageJsonPush();
        message.setMessageId(request.getMessageId());
        message.setFromOperatorNo(request.getFromOperatorNo());
        message.setToOperatorNo(memberId);
        message.setGroupId(groupId);
        message.setChatId(request.getChatId());
        message.setChatType(request.getChatType());
        message.setContent(request.getContent());
        message.setCategory(request.getCategory());
        message.setSequence(request.getSequence());
        kafkaTemplate.send(TOPIC_MESSAGE_PUSH, JSONObject.toJSONString(message));
    }

    public void sendMessageFetch(Long chatId, List<String> memberIds) {
        for (String memberId : memberIds) {
            sendMessageFetch(chatId, memberId);
        }
    }

    public void sendMessageFetch(Long chatId, String toOperatorNo) {
        MessageJsonFetch message = new MessageJsonFetch();
        message.setToOperatorNo(toOperatorNo);
        message.setChatType(Constants.CHAT_TYPE_C2G);
        message.setChatId(chatId);
        kafkaTemplate.send(TOPIC_MESSAGE_FETCH, JSONObject.toJSONString(message));
    }


}
