package com.chaos.im.chat.domain;

import lombok.Data;

import javax.validation.constraints.NotNull;
import java.util.Date;

@Data
public class CreateCustomerApplyReq {

    @NotNull(message = "订单号不能为空")
    private String orderNo;
    @NotNull(message = "订单时间不能为空")
//    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    private Date orderTime;
    @NotNull(message = "用户名不能为空")
    private String userName;
    @NotNull(message = "用户手机号不能为空")
    private String userMobile;
    @NotNull(message = "头像不能为空")
    private String headUrl;
//    @NotNull(message = "商户编号不能为空")
    private String merchantNo;
//    @NotNull(message = "商户名称不能为空")
    private String merchantName;
//    @NotNull(message = "商户手机号不能为空")
    private String merchantMobile;
//    @NotNull(message = "商户头像不能为空")
    private String merchantHeadUrl;
    @NotNull(message = "用户操作员编号不能为空")
    private String userOperatorNo;
    private String riderNo;
    private String riderName;
    private String riderHeadUrl;
    private String riderMobile;
    //  @NotNull(message = "收货人不能为空")
    private String receiverName;
    //  @NotNull(message = "收货地址不能为空")
    private String receiverMobile;
    private String customerNo;
    @NotNull(message = "类型不能为空")
    private Integer type;
    @NotNull(message = "语言不能为空")
    private String lang;
    private Integer readStatus;
    private Integer interveneStatus;
    private Integer handleStatus;

//    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    private Date applyTime;

    @NotNull(message = "chatId不能为空")
    private Long chatId;
}
