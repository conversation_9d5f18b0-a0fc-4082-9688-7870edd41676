package com.chaos.im.chat.domain;

import java.time.LocalDateTime;

import lombok.Getter;
import lombok.Setter;

@Setter
@Getter
public class CustomerChat {

    private String customerNo;

    private Long chatId;

    private Integer type;

    private String peerId;

    private String memberId;

    private String nickname;

    private String avatar;

    private LocalDateTime activeTime;

    private LastMessageDTO lastMessage;

    private String ext;

}
