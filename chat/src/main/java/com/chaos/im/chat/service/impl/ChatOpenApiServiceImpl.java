package com.chaos.im.chat.service.impl;

import cn.hutool.core.util.ObjectUtil;
import cn.hutool.json.JSONUtil;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.chaos.im.chat.domain.*;
import com.chaos.im.chat.service.ApplyCustomersRefService;
import com.chaos.im.chat.service.ChatMemberService;
import com.chaos.im.chat.service.ChatOpenApiService;
import com.chaos.im.chat.service.CustomerApplyService;
import com.chaos.keep.alive.common.core.exception.BusinessException;
import com.chaos.keep.alive.common.im.constant.Constants;
import com.chaos.keep.alive.common.im.constant.ImConstants;
import com.chaos.keep.alive.common.im.domain.ImCodeEnum;
import com.outstanding.framework.core.PendingException;
import com.outstanding.framework.core.ResponseDTO;

import lombok.extern.slf4j.Slf4j;
import org.redisson.api.*;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.RequestBody;

import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.concurrent.TimeUnit;
import java.util.concurrent.TimeoutException;
import java.util.stream.Collectors;

@Service
@Slf4j
public class ChatOpenApiServiceImpl implements ChatOpenApiService {

    @Autowired
    private CustomerApplyService customerApplyService;

    @Autowired
    private ChatMemberService chatMemberService;

    @Autowired
    private RedissonClient redissonClient;

    @Autowired
    private ApplyCustomersRefService applyCustomersRefService;

    @Override
    public ResponseDTO<?> page(@RequestBody @Validated PageCustomerApplyReq customerApply) {
        if (customerApply.getPageSize() > 20) {
            throw new BusinessException("pageSize不能超过20");
        }

        Page<CustomerApplyDO> page = new Page<>(customerApply.getCurrent(), customerApply.getPageSize());
        IPage<CustomerApplyDO> result = customerApplyService.page(page, customerApply);

        log.info("get customerApply from db :{}", JSONUtil.toJsonStr(result.getRecords()));
        List<PageCustomerApplyResp> respVO = result.getRecords().stream().map(customerApplyDO -> {
            PageCustomerApplyResp resp = new PageCustomerApplyResp();
            BeanUtils.copyProperties(customerApplyDO, resp);
            String orderNo = customerApplyDO.getOrderNo();
            GetChatByOrderNoReq getChatByOrderNoReq = new GetChatByOrderNoReq();
            getChatByOrderNoReq.setOrderNo(orderNo);
            getChatByOrderNoReq.setCustomerNo(customerApply.getCustomerNo());
            ResponseDTO<List<CustomerChatVO>> chatList =  getByOrderNo(getChatByOrderNoReq);
            resp.setChatList(chatList.getData());
            return resp;
        }).collect(Collectors.toList());

        IPage<PageCustomerApplyResp> pageResp = new Page<>();
        pageResp.setRecords(respVO);
        pageResp.setTotal(result.getTotal());
        pageResp.setCurrent(customerApply.getCurrent());
        pageResp.setSize(customerApply.getPageSize());

        return ResponseDTO.creatDTO(pageResp);
    }

    @Override
    public ResponseDTO<List<CustomerChatVO>> getByOrderNo(GetChatByOrderNoReq req) {
        List<CustomerApplyDO> customerApply = customerApplyService.getByOrderNo(req.getOrderNo());
        if(customerApply.isEmpty()){
            throw new PendingException(ImCodeEnum.I1040,"订单号没有申请单");
        }

        List<CustomerChatVO> customerChatVOList =  customerApply.stream().map(customerApplyDO -> {
            Long chatId =  customerApplyDO.getChatId();
            ChatMemberDO chatMemberDO = chatMemberService.getOneByChatId(chatId);
            if(Objects.isNull(chatMemberDO)){
                return null;
            }

            CustomerChatVO customerChatVO = new CustomerChatVO();
            BeanUtils.copyProperties(chatMemberDO, customerChatVO);

            customerChatVO.setBizType(customerApplyDO.getType());
            customerChatVO.setReceiverName(customerApplyDO.getReceiverName());
            customerChatVO.setUserName(customerApplyDO.getUserName());
            customerChatVO.setRiderName(customerApplyDO.getRiderName());
            customerChatVO.setRiderAvatar(customerApplyDO.getRiderHeadUrl());
            customerChatVO.setUserName(customerApplyDO.getUserName());
            customerChatVO.setRiderName(customerApplyDO.getRiderName());
            customerChatVO.setGroupId(Long.parseLong(chatMemberDO.getPeerId()));
            customerChatVO.setApplyId(customerApplyDO.getId());
            customerChatVO.setUserOperatorNo(customerApplyDO.getUserOperatorNo());
            customerChatVO.setRiderOperatorNo(customerApplyDO.getRiderOperatorNo());
            customerChatVO.setUserMobile(customerApplyDO.getUserMobile());
            customerChatVO.setRiderMobile(customerApplyDO.getRiderMobile());
            return customerChatVO;

        }).filter(Objects::nonNull).collect(Collectors.toList());
        Map<String, RFuture<Map<String, Object>>> lastMessageFuture = new HashMap<>();

        RBatch batch = redissonClient.createBatch();
        RBatch unReadCountBatch = redissonClient.createBatch();
        Map<String, RFuture<Long>> unReadCountFuture = new HashMap<>();

        customerChatVOList.forEach(it->{
            if (Objects.equals(it.getType(), Constants.CHAT_TYPE_C2C)) {
                RMapAsync<String,Object> mapAsync = batch.getMap(ImConstants.C2C_LAST_MESSAGE_PREFIX + it.getChatId() + ImConstants.SEPARATOR + req.getCustomerNo());
                lastMessageFuture.put(ImConstants.C2C_LAST_MESSAGE_PREFIX + it.getChatId() + ImConstants.SEPARATOR +req.getCustomerNo(),mapAsync.readAllMapAsync());
                RAtomicLongAsync longAsync = unReadCountBatch.getAtomicLong(ImConstants.C2C_UNREAD_COUNT_PREFIX + it.getChatId() + ImConstants.SEPARATOR + req.getCustomerNo());
                unReadCountFuture.put(ImConstants.C2C_UNREAD_COUNT_PREFIX + it.getChatId() + ImConstants.SEPARATOR +req.getCustomerNo(),longAsync.getAsync());
            } else {
                RMapAsync<String,Object> mapAsync = batch.getMap(ImConstants.C2G_LAST_MESSAGE_PREFIX + it.getChatId());
                lastMessageFuture.put(ImConstants.C2G_LAST_MESSAGE_PREFIX + it.getChatId(),mapAsync.readAllMapAsync());
                RAtomicLongAsync longAsync = unReadCountBatch.getAtomicLong(ImConstants.C2G_UNREAD_COUNT_PREFIX + it.getChatId() + ImConstants.SEPARATOR +req.getCustomerNo());
                unReadCountFuture.put(ImConstants.C2G_UNREAD_COUNT_PREFIX + it.getChatId() + ImConstants.SEPARATOR + req.getCustomerNo(),longAsync.getAsync());
            }
        });

        batch.execute();

        unReadCountBatch.execute();

        // 获取结果集

        Map<String, Map<String, Object>> lastMsgResults = new HashMap<>();
        for (Map.Entry<String, RFuture<Map<String, Object>>> entry : lastMessageFuture.entrySet()) {
            try {
                // 增加超时时间到1秒
                Map<String, Object> result = entry.getValue().get(1000, TimeUnit.MILLISECONDS);
                if (result != null) {
                    lastMsgResults.put(entry.getKey(), result);
                }
            }catch (TimeoutException e){
                log.warn("get lastMessage timeout for key:[{}]", entry.getKey());
            }catch (Exception e){
                log.error("get lastMessage error: [{}] key:[{}]", e.getMessage(), entry.getKey(), e);
            }
        }

        Map<String, Long> unReadCountResults = new HashMap<>();
        for (Map.Entry<String, RFuture<Long>> entry : unReadCountFuture.entrySet()) {
            try {
                // 增加超时时间到1秒，并提供默认值
                Long result = entry.getValue().get(1000, TimeUnit.MILLISECONDS);
                unReadCountResults.put(entry.getKey(), result != null ? result : 0L);
            }catch (TimeoutException e){
                log.warn("get unReadCount timeout, using default value 0 for key:[{}]", entry.getKey());
                unReadCountResults.put(entry.getKey(), 0L);
            }catch (Exception e){
                log.error("get unReadCount error: [{}] key:[{}], using default value 0", e.getMessage(), entry.getKey(), e);
                unReadCountResults.put(entry.getKey(), 0L);
            }
        }

        customerChatVOList.stream().forEach(it -> {
            LastMessageDTO lastMessageVO = new LastMessageDTO();
            if (Objects.equals(it.getType(), Constants.CHAT_TYPE_C2C)) {
                Map<String,Object> lastMsgMap = lastMsgResults.get(ImConstants.C2C_LAST_MESSAGE_PREFIX + it.getChatId() + ImConstants.SEPARATOR + req.getCustomerNo());
                if (ObjectUtil.isNotNull(lastMsgMap)) {
                    log.info("get c2clastMsg from redis :{}", JSONUtil.toJsonStr(lastMsgMap));
                    lastMessageVO = convertMapToLastMessageVO(lastMsgMap);
                    it.setLastMessage(lastMessageVO);
                }

                Long unReadCount = unReadCountResults.get(ImConstants.C2C_UNREAD_COUNT_PREFIX + it.getChatId() + ImConstants.SEPARATOR +req.getCustomerNo());
                lastMessageVO.setUnReadCount(unReadCount==null?0:unReadCount);
            } else {
                Map<String,Object> lastMsgMap = lastMsgResults.get(ImConstants.C2G_LAST_MESSAGE_PREFIX + it.getChatId());
                if (ObjectUtil.isNotNull(lastMsgMap)) {
                    log.info("get c2glastMsg from redis:{}", JSONUtil.toJsonStr(lastMsgMap));
                    lastMessageVO = convertMapToLastMessageVO(lastMsgMap);
                    it.setLastMessage(lastMessageVO);
                }
                Long unReadCount = unReadCountResults.get(ImConstants.C2G_UNREAD_COUNT_PREFIX + it.getChatId() + ImConstants.SEPARATOR + req.getCustomerNo());
                lastMessageVO.setUnReadCount((unReadCount == null ? 0 : unReadCount));
            }
        });
        return ResponseDTO.creatDTO(customerChatVOList);
    }

    @Override
    public ResponseDTO<Boolean> updateHandleStatus(UpdateOrderApplyStatusReq req) {
        boolean result = customerApplyService.updateHandleStatus(req.getOrderNo(), req.getStatus());
        return ResponseDTO.creatDTO(result);
    }

    @Override
    public ResponseDTO<?> updateReadStatus(UpdateOrderApplyStatusReq req) {
        boolean result = customerApplyService.updateReadStatus(req.getOrderNo(), req.getStatus());
        return ResponseDTO.creatDTO(result);
    }

    private LastMessageDTO convertMapToLastMessageVO(Map<String, Object> lastMsgMap) {
        if (lastMsgMap == null || lastMsgMap.isEmpty()) {
            return new LastMessageDTO();
        }

        LastMessageDTO lastMessageVO = new LastMessageDTO();

        // 安全地转换各个字段
        if (lastMsgMap.get("chatId") != null) {
            lastMessageVO.setChatId(Long.valueOf(lastMsgMap.get("chatId").toString()));
        }
        if (lastMsgMap.get("messageId") != null) {
            lastMessageVO.setMessageId(Long.valueOf(lastMsgMap.get("id").toString()));
        }
        if (lastMsgMap.get("fromOperatorNo") != null) {
            lastMessageVO.setFromOperatorNo(lastMsgMap.get("fromOperatorNo").toString());
        }
        if (lastMsgMap.get("toOperatorNo") != null) {
            lastMessageVO.setToOperatorNo(lastMsgMap.get("toOperatorNo").toString());
        }
        if (lastMsgMap.get("category") != null) {
            lastMessageVO.setCategory(Integer.valueOf(lastMsgMap.get("category").toString()));
        }
        if (lastMsgMap.get("content") != null) {
            lastMessageVO.setContent(lastMsgMap.get("content").toString());
        }
        if (lastMsgMap.get("appId") != null) {
            lastMessageVO.setAppId(lastMsgMap.get("appId").toString());
        }
        if (lastMsgMap.get("sequence") != null) {
            lastMessageVO.setSequence(Long.valueOf(lastMsgMap.get("sequence").toString()));
        }
        if (lastMsgMap.get("timestamp") != null) {
            lastMessageVO.setTimestamp(Long.valueOf(lastMsgMap.get("timestamp").toString()));
        }

        return lastMessageVO;
    }




    @Override
    public ResponseDTO<?> acceptCustomerApply(AcceptApplyReq reqDTO) {
        customerApplyService.acceptCustomerApply(reqDTO);
        return ResponseDTO.creatDTO();
    }

    @Override
    public ResponseDTO<List<ApplyCustomersRefDTO>> listCustomerByApplyId(CustomerListReq req) {

        LambdaQueryWrapper<ApplyCustomersRef> applyCustomersRefLambdaQueryWrapper = new LambdaQueryWrapper<>();
        applyCustomersRefLambdaQueryWrapper.eq(ApplyCustomersRef::getApplyId, req.getApplyId());
        List<ApplyCustomersRef> list = applyCustomersRefService.list(applyCustomersRefLambdaQueryWrapper);
        List<ApplyCustomersRefDTO> res = list.stream().map(acr -> {
            ApplyCustomersRefDTO applyCustomersRefDTO = new ApplyCustomersRefDTO();
            BeanUtils.copyProperties(acr, applyCustomersRefDTO);
            return applyCustomersRefDTO;
        }).collect(Collectors.toList());
        return ResponseDTO.creatDTO( res);
    }

    @Override
    public boolean isExistCustomerApply(IsExistCustomerApplyReq req) {

        LambdaQueryWrapper<CustomerApplyDO> lambdaQueryWrapper = new LambdaQueryWrapper<>();
        if(req.getType()==1 && req.getOperatorNo()!=null){
            lambdaQueryWrapper.eq(CustomerApplyDO::getUserOperatorNo,req.getOperatorNo());
        }
        if(req.getType()==2 && req.getOperatorNo()!=null){
            lambdaQueryWrapper.eq(CustomerApplyDO::getRiderOperatorNo,req.getOperatorNo());
        }

        lambdaQueryWrapper.eq(CustomerApplyDO::getOrderNo,req.getOrderNo());

        lambdaQueryWrapper.eq(CustomerApplyDO::getType,req.getType());

        lambdaQueryWrapper.last("limit 1");

        CustomerApplyDO customerApplyDO = customerApplyService.getOne(lambdaQueryWrapper);

        return customerApplyDO!=null;
    }
}
