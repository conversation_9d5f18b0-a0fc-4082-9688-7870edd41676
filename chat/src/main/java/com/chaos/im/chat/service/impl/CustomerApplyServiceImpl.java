package com.chaos.im.chat.service.impl;

import cn.hutool.core.util.ObjectUtil;
import cn.hutool.core.util.StrUtil;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.conditions.update.LambdaUpdateWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.chaos.im.c2g.group.api.GroupApi;
import com.chaos.im.chat.domain.*;
import com.chaos.im.chat.mapper.CustomerApplyMapper;
import com.chaos.im.chat.service.ApplyCustomersRefService;
import com.chaos.im.chat.service.ChatMemberService;
import com.chaos.im.chat.service.CustomerApplyService;
import com.chaos.keep.alive.common.core.exception.BusinessException;
import lombok.extern.slf4j.Slf4j;
import org.apache.dubbo.config.annotation.DubboReference;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.time.LocalDateTime;
import java.util.Arrays;
import java.util.Date;
import java.util.List;

/**
 * 客户申请服务实现类
 *
 * <AUTHOR>
 */
@Slf4j
@Service
public class CustomerApplyServiceImpl extends ServiceImpl<CustomerApplyMapper, CustomerApplyDO> implements CustomerApplyService {

    @Autowired
    private CustomerApplyMapper customerApplyMapper;


    @Autowired
    private ChatMemberService chatMemberService;

    @DubboReference(version = "1.0.0")
    private GroupApi groupApi;
    @Autowired
    private ApplyCustomersRefService applyCustomersRefService;

    @Override
    public IPage<CustomerApplyDO> page(Page<CustomerApplyDO> page, PageCustomerApplyReq customerApply) {
        LambdaQueryWrapper<CustomerApplyDO> queryWrapper = new LambdaQueryWrapper<>();

        if (StrUtil.isNotBlank(customerApply.getOrderNo())) {
            queryWrapper.eq(CustomerApplyDO::getOrderNo, customerApply.getOrderNo());
        }
        if (ObjectUtil.isNotNull(customerApply.getOrderTime())) {
            queryWrapper.eq(CustomerApplyDO::getOrderTime, customerApply.getOrderTime());
        }
        if (StrUtil.isNotBlank(customerApply.getUserName())) {
            queryWrapper.like(CustomerApplyDO::getUserName, customerApply.getUserName());
        }
        if (StrUtil.isNotBlank(customerApply.getUserMobile())) {
            queryWrapper.eq(CustomerApplyDO::getUserMobile, customerApply.getUserMobile());
        }
        if (StrUtil.isNotBlank(customerApply.getHeadUrl())) {
            queryWrapper.eq(CustomerApplyDO::getHeadUrl, customerApply.getHeadUrl());
        }
        if (StrUtil.isNotBlank(customerApply.getUserOperatorNo())) {
            queryWrapper.eq(CustomerApplyDO::getUserOperatorNo, customerApply.getUserOperatorNo());
        }
        if (StrUtil.isNotBlank(customerApply.getRiderOperatorNo())) {
            queryWrapper.eq(CustomerApplyDO::getRiderOperatorNo, customerApply.getRiderOperatorNo());
        }
        if (ObjectUtil.isNotNull(customerApply.getType())) {
            queryWrapper.eq(CustomerApplyDO::getType, customerApply.getType());
        }
        if (StrUtil.isNotBlank(customerApply.getLang())) {
            queryWrapper.eq(CustomerApplyDO::getLang, customerApply.getLang());
        }
        if (ObjectUtil.isNotNull(customerApply.getReadStatus())) {
            queryWrapper.eq(CustomerApplyDO::getReadStatus, customerApply.getReadStatus());
        }
        if (ObjectUtil.isNotNull(customerApply.getInterveneStatus())) {
            queryWrapper.eq(CustomerApplyDO::getInterveneStatus, customerApply.getInterveneStatus());
        }
        if (ObjectUtil.isNotNull(customerApply.getHandleStatus())) {
            queryWrapper.eq(CustomerApplyDO::getHandleStatus, customerApply.getHandleStatus());
        }
        if (ObjectUtil.isNotNull(customerApply.getChatId())) {
            queryWrapper.eq(CustomerApplyDO::getChatId, customerApply.getChatId());
        }
        if(ObjectUtil.isNotNull(customerApply.getStartApplyTime())){
            queryWrapper.ge(CustomerApplyDO::getApplyTime, customerApply.getStartApplyTime());
        }
        if(ObjectUtil.isNotNull(customerApply.getEndApplyTime())){
            queryWrapper.le(CustomerApplyDO::getApplyTime, customerApply.getEndApplyTime());
        }

//        queryWrapper.select(CustomerApplyDO::getOrderNo,
//                CustomerApplyDO::getOrderTime,
//                CustomerApplyDO::getUserOperatorNo,
//                CustomerApplyDO::getUserName,
//                CustomerApplyDO::getReceiverName,
//                CustomerApplyDO::getReceiverMobile,
//                CustomerApplyDO::getUserMobile,
//                CustomerApplyDO::getHeadUrl,
//                CustomerApplyDO::getMerchantNo,
//                CustomerApplyDO::getMerchantName,
//                CustomerApplyDO::getMerchantMobile,
//                CustomerApplyDO::getMerchantHeadUrl,
//                CustomerApplyDO::getReadStatus,
//                CustomerApplyDO::getInterveneStatus,
//                CustomerApplyDO::getLang,
//                CustomerApplyDO::getHandleStatus);
        queryWrapper.orderByDesc(CustomerApplyDO::getApplyTime)
                .groupBy(CustomerApplyDO::getOrderNo);

        return customerApplyMapper.selectPage(page, queryWrapper);
    }

    @Override
    public CustomerApplyDO getById(Long id) {
        if (ObjectUtil.isNull(id)) {
            return null;
        }
        return customerApplyMapper.selectById(id);
    }

    @Override
    public List<CustomerApplyDO> getByOrderNo(String orderNo) {
        if (StrUtil.isBlank(orderNo)) {
            return null;
        }
        LambdaQueryWrapper<CustomerApplyDO> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.eq(CustomerApplyDO::getOrderNo, orderNo);
        return customerApplyMapper.selectList(queryWrapper);
    }

    @Override
    public List<CustomerApplyDO> listByChatId(Long chatId) {
        if (ObjectUtil.isNull(chatId)) {
            return null;
        }
        LambdaQueryWrapper<CustomerApplyDO> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.eq(CustomerApplyDO::getChatId, chatId);
        queryWrapper.orderByDesc(CustomerApplyDO::getApplyTime);
        return customerApplyMapper.selectList(queryWrapper);
    }

    @Override
    public List<CustomerApplyDO> listByUserMobile(String userMobile) {
        if (StrUtil.isBlank(userMobile)) {
            return null;
        }
        LambdaQueryWrapper<CustomerApplyDO> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.eq(CustomerApplyDO::getUserMobile, userMobile);
        queryWrapper.orderByDesc(CustomerApplyDO::getApplyTime);
        return customerApplyMapper.selectList(queryWrapper);
    }

    @Override
    public List<CustomerApplyDO> listByUserName(String userName) {
        if (StrUtil.isBlank(userName)) {
            return null;
        }
        LambdaQueryWrapper<CustomerApplyDO> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.like(CustomerApplyDO::getUserName, userName);
        queryWrapper.orderByDesc(CustomerApplyDO::getApplyTime);
        return customerApplyMapper.selectList(queryWrapper);
    }

    @Override
    public List<CustomerApplyDO> listByTimeRange(Date startTime, Date endTime) {
        LambdaQueryWrapper<CustomerApplyDO> queryWrapper = new LambdaQueryWrapper<>();
        if (ObjectUtil.isNotNull(startTime)) {
            queryWrapper.ge(CustomerApplyDO::getApplyTime, startTime);
        }
        if (ObjectUtil.isNotNull(endTime)) {
            queryWrapper.le(CustomerApplyDO::getApplyTime, endTime);
        }
        queryWrapper.orderByDesc(CustomerApplyDO::getApplyTime);
        return customerApplyMapper.selectList(queryWrapper);
    }

    @Override
    @Transactional
    public boolean save(CustomerApplyDO customerApply) {
        if (ObjectUtil.isNull(customerApply)) {
            return false;
        }
        if (ObjectUtil.isNull(customerApply.getApplyTime())) {
            customerApply.setApplyTime(new Date());
        }
        CustomerApplyDO customerApplyDO = selectByOrderNoAndType(customerApply.getOrderNo(), customerApply.getType(),customerApply.getUserOperatorNo());
        if (customerApplyDO != null) {
            throw new BusinessException("申请单已经存在");
        }
        customerApply.setGmtCreate(LocalDateTime.now());
        customerApply.setGmtModified(LocalDateTime.now());
        return customerApplyMapper.insert(customerApply) > 0;
    }

    @Override
    @Transactional
    public boolean updateById(CustomerApplyDO customerApply) {
        if (ObjectUtil.isNull(customerApply) || ObjectUtil.isNull(customerApply.getId())) {
            return false;
        }
        return customerApplyMapper.updateById(customerApply) > 0;
    }

    @Override
    @Transactional
    public boolean updateReadStatus(String  orderNo, Integer readStatus) {
        if (ObjectUtil.isNull(orderNo) || ObjectUtil.isNull(readStatus)) {
            return false;
        }
        LambdaUpdateWrapper<CustomerApplyDO> updateWrapper = new LambdaUpdateWrapper<>();
        updateWrapper.eq(CustomerApplyDO::getOrderNo, orderNo);
        updateWrapper.set(CustomerApplyDO::getReadStatus, readStatus);
        return customerApplyMapper.update(null, updateWrapper) > 0;
    }

    @Override
    @Transactional
    public boolean updateHandleStatus(String orderNo, Integer status) {
        if (ObjectUtil.isNull(orderNo) || ObjectUtil.isNull(status)) {
            return false;
        }
        LambdaUpdateWrapper<CustomerApplyDO> updateWrapper = new LambdaUpdateWrapper<>();
        updateWrapper.eq(CustomerApplyDO::getOrderNo, orderNo);
        updateWrapper.set(CustomerApplyDO::getHandleStatus, status);
        return customerApplyMapper.update(null, updateWrapper) > 0;
    }

    @Override
    @Transactional
    public boolean removeById(Long id) {
        if (ObjectUtil.isNull(id)) {
            return false;
        }
        return customerApplyMapper.deleteById(id) > 0;
    }

    @Override
    @Transactional
    public boolean removeByIds(List<Long> ids) {
        if (ObjectUtil.isEmpty(ids)) {
            return false;
        }
        return customerApplyMapper.deleteBatchIds(ids) > 0;
    }

    @Override
    public void acceptCustomerApply(AcceptApplyReq reqDTO) {
        LambdaQueryWrapper<CustomerApplyDO> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.eq(CustomerApplyDO::getId, reqDTO.getApplyId());
        List<CustomerApplyDO> customerApplyDOS = customerApplyMapper.selectList(queryWrapper);
        customerApplyDOS.forEach(customerApplyDO -> {
//            customerApplyDO.setReadStatus(1);
            customerApplyDO.setInterveneStatus(1);

            LambdaQueryWrapper<ApplyCustomersRef> applyCustomersRefLambdaQueryWrapper  = new LambdaQueryWrapper<>();
            applyCustomersRefLambdaQueryWrapper.eq(ApplyCustomersRef::getApplyId, customerApplyDO.getId());
            applyCustomersRefLambdaQueryWrapper.eq(ApplyCustomersRef::getCustomerNo, reqDTO.getCustomerNo());

            List<ApplyCustomersRef> applyCustomersRefs = applyCustomersRefService.list(applyCustomersRefLambdaQueryWrapper);
            if(applyCustomersRefs.isEmpty() || applyCustomersRefs.size()==0){
                ApplyCustomersRef ref = new ApplyCustomersRef();
                ref.setApplyId(customerApplyDO.getId());
                ref.setCustomerNo(reqDTO.getCustomerNo());
                ref.setGmtCreate(LocalDateTime.now());
                ref.setGmtModified(LocalDateTime.now());
                applyCustomersRefService.save(ref);
            }


            String peerId =  chatMemberService.getPeerIdByChatId(customerApplyDO.getChatId());

            if (peerId != null) {
                //加入群组
                log.debug("加入群组,peerId:{}", peerId);
                groupApi.invite(Long.parseLong(peerId), Arrays.asList(reqDTO.getCustomerNo()));
            }else{
                log.debug("找不到groupId");
                throw new BusinessException("找不到群组");
            }

            customerApplyMapper.updateById(customerApplyDO);
        });
    }

    @Override
    public CustomerApplyDO selectByOrderNoAndType(String orderNo, Integer type, String userOperatorNo) {

        LambdaQueryWrapper<CustomerApplyDO> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.eq(CustomerApplyDO::getOrderNo, orderNo);
        queryWrapper.eq(CustomerApplyDO::getType, type);
        queryWrapper.eq(CustomerApplyDO::getUserOperatorNo, userOperatorNo);
        return getOne(queryWrapper);
    }
}
