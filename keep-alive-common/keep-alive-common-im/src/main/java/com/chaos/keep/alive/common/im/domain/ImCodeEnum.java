package com.chaos.keep.alive.common.im.domain;

import com.outstanding.framework.core.ReturnCode;

public enum ImCodeEnum implements ReturnCode {
    I1001("I1001","该内容含敏感词"),
    I1002("I1002","IM消息发送失败"),
    I1003("I1003","获取IM会话列表失败"),
    I1004("I1004","操作员编号不能为空"),
    I1005("I1005","获取会话聊天记录失败"),
    I1006("I1006","代聊消息扩展内容错误"),
    I1007("I1007","获取公告失败，操作员未在IM注册"),
    I1008("I1008","获取公告失败，传入语言为空"),
    I1009("I1009","获取IMtoken失败"),
    I1010("I1010","消息类型转换失败"),
    I1011("I1011","创建群组失败"),
    I1012("I1012","解散群组失败"),
    I1013("I1013","踢出群组失败"),
    I1014("I1014","禁言群组失败"),
    I1015("I1015","群组解除禁言失败"),
    I1016("I1016","获取群组信息失败"),
    I1017("I1017","获取群成员信息失败"),
    I1018("I1018","申请单编号不存在"),
    I1019("I1019","将用户拉入群组失败"),
    I1020("I1020","查询用户IM绑定信息失败"),
    I1021("I1021","修改群组信息失败"),
    I1022("I1022","群组ID不存在"),
    I1023("I1023","获取群id参数失败"),
    I1024("I1024","获取IM注册信息失败"),
    I1025("I1025","当前订单已创建群聊，请勿重复创建"),
    I1026("I1026","已邀请客服加入，请耐心等待"),
    I1027("I1027","当前申请已被受理"),
    I1028("I1028","只能对“处理中”的申请进行处理"),
    I1029("I1029","群成员操作员编号和类型参数不能为空"),


    I1040("I1040","用户快捷咨询操作不存在"),



    I1041("I1041","客服素材不存在"),
    I1042("I1042","客服公告不存在"),

    I1030("I1030","将用户拉入黑名单失败"),
    I1031("I1031","该反馈已处理，请勿重复操作"),
    I1032("I1032","获取用户黑名单列表失败"),
    I1033("I1033","该黑名单已解除，请勿重复操作"),
    I1034("I1034","将用户移除黑名单失败"),
    I1035("I1035","用户手机号非平台注册用户"),
    I1036("I1036","当前用户已加入黑名单，请勿重复添加"),
    I1037("I1037","当前群聊已解散"),
    I1038("I1038","当前用户未注册IM账号"),
    I1039("I1039","该敏感词已存在"),
    I1043("I1043","当前操作员不是客服，请先添加为客服再进行操作"),
    I1044("I1044","会话场景不能为空"),
    I1045("I1045","你已提交反馈，请勿重复提交"),
    I1046("I1046","订单号没有申请单"),
    ;

    ImCodeEnum(String code, String message){
        this.code = code;
        this.message = message;
    }
    private String code;
    private String message;

    public String getCode() {
        return this.code;
    }

    public String getMessage() {
        return this.message;
    }
}
